import { getLLMConfig } from '@/config';
import { Random<PERSON>ord, RandomWordDetailSchema, RandomWordSchema } from '@/models';
import { Difficulty, Language, Word } from '@prisma/client';
import OpenAI from 'openai';
import { zodResponseFormat } from 'openai/helpers/zod';
import { z } from 'zod';
import { WordService } from '.';

// Interface for existing method, updated
interface GenerateParagraphParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	count: number; // Added count for multiple paragraphs
	sentenceCount?: number; // desired number of sentences
}

// Interfaces for existing methods (remains the same, but listed for completeness)
interface GenerateRandomTermsParams {
	userId: string;
	keywordTerms: string[];
	excludesTerms: string[];
	maxTerms: number;
	excludeCollectionIds: string[];
	source_language: Language;
	target_language: Language;
}

interface GenerateExercisesParams {
	paragraph: string;
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
}

export interface AnalyzeParagraphRequest {
	content: string;
	language: Language;
}

// Interfaces for Question & Answer features
export interface GenerateQuestionsParams {
	paragraph: string;
	language: Language;
	questionCount: number;
}

// Interface for combined paragraph and questions generation
export interface GenerateParagraphWithQuestionsParams {
	keywords: string[];
	language: Language;
	difficulty: Difficulty;
	sentenceCount?: number;
	questionCount: number;
}

export interface ParagraphWithQuestionsResult {
	paragraph: string;
	questions: string[];
}

export interface EvaluateAnswersParams {
	paragraph: string;
	questions: string[];
	answers: string[];
	qna_language: Language; // Language of the questions and answers
	feedback_native_language: Language; // User's native language for feedback
}

export interface GrammarPracticeParams {
	keywords: string[];
	language: Language;
	source_language: Language;
	target_language: Language;
	difficulty: Difficulty;
	count: number;
	sentenceCount?: number;
	errorDensity?: 'low' | 'medium' | 'high';
}

export interface GrammarPracticeResultItem {
	paragraphWithErrors: string;
	correctedParagraph: string;
	allErrors: Array<{
		errorText: string;
		correctedText: string;
		errorType: string;
		explanation: {
			source_language: string;
			target_language: string;
		};
	}>;
}

export interface AnswerEvaluationResult {
	question: string;
	answer: string;
	feedback: {
		qna_feedback_text: string; // Feedback in the Q&A language
		native_feedback_text: string; // Feedback in the user's native language
	};
	score?: number;
	is_correct?: boolean;
	suggested_answer?: string;
}

// Interfaces for the new evaluateTranslation method
export interface EvaluateTranslationParams {
	original_text: string;
	translated_text: string;
	source_language: Language;
	target_language: Language;
}

export interface TranslationEvaluationResult {
	feedback: {
		source_language: string;
		target_language: string;
	};
	score?: number;
	suggestions?: {
		source_language: string[];
		target_language: string[];
	};
}

// Zod schemas for new/updated methods
const ExerciseSchema = z.object({
	type: z.string(),
	question: z.string(),
	answer: z.string(),
	options: z.array(z.string()).optional(),
	explanation: z.string().optional(),
});

const ExercisesResponseSchema = z.object({
	exercises: z.array(ExerciseSchema),
});

// Zod schema for generating questions
const GeneratedQuestionsSchema = z.object({
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for combined paragraph and questions generation
const ParagraphWithQuestionsSchema = z.object({
	paragraph: z.string().describe('A generated paragraph based on the provided keywords.'),
	questions: z
		.array(z.string())
		.describe('An array of generated questions based on the paragraph.'),
});

// Zod schema for evaluating a single answer
const AnswerEvaluationSchema = z.object({
	question: z.string().describe('The original question this evaluation pertains to.'),
	answer: z.string().describe("The user's answer that was evaluated."),
	feedback: z
		.object({
			qna_feedback_text: z
				.string()
				.describe(
					"Detailed feedback in the Q&A language ({qna_language}) on the user's answer, explaining its strengths and weaknesses."
				),
			native_feedback_text: z
				.string()
				.describe(
					"The same detailed feedback, but translated or adapted into the user's native language ({feedback_native_language}) for better understanding."
				),
		})
		.describe(
			"Detailed feedback on the user's answer, provided in both the Q&A language and the user's native language."
		),
	score: z
		.number()
		.min(1)
		.max(5)
		.optional()
		.describe('A score from 1 (poor) to 5 (excellent) for the answer.'),
	is_correct: z
		.boolean()
		.optional()
		.describe(
			'A simple true/false indicating if the answer is fundamentally correct regarding the paragraph content.'
		),
	suggested_answer: z
		.string()
		.optional()
		.describe(
			'An example of an ideal or improved answer to the question, based on the paragraph.'
		),
});

// Zod schema for evaluating all answers
const AllAnswersEvaluationSchema = z.object({
	evaluations: z
		.array(AnswerEvaluationSchema)
		.describe('An array of evaluations, one for each question-answer pair.'),
});

const TranslationEvaluationSchema = z.object({
	feedback: z
		.object({
			source_language: z
				.string()
				.describe('Overall feedback in the original text language (source_language).'),
			target_language: z
				.string()
				.describe("Overall feedback in the user's translation language (target_language)."),
		})
		.describe(
			"Overall feedback on the translation's quality, correctness, and areas for improvement, provided in both source and target languages."
		),
	score: z
		.number()
		.min(1)
		.max(10)
		.optional()
		.describe('An overall score from 1 (poor) to 10 (excellent) for the translation.'),
	suggestions: z
		.object({
			source_language: z
				.array(z.string())
				.optional()
				.describe(
					'Specific suggestions for improving the translation in the source_language, if any.'
				),
			target_language: z
				.array(z.string())
				.optional()
				.describe(
					'Specific suggestions for improving the translation in the target_language, if any.'
				),
		})
		.optional()
		.describe(
			'Specific suggestions for improving the translation, provided in both source and target languages, if any.'
		),
});

// New combined schema for grammar practice
const GrammarPracticeResponseSchema = z.object({
	paragraphs: z.array(
		z.object({
			paragraphWithErrors: z
				.string()
				.describe('Array of paragraphs containing intentional errors for practice.'),
			correctedParagraph: z
				.string()
				.describe('Array of the same paragraphs with all errors corrected.'),
			allErrors: z
				.array(
					z.object({
						errorText: z
							.string()
							.describe('The incorrect text found in the paragraph.'),
						correctedText: z.string().describe('The corrected version of the error.'),
						errorType: z
							.string()
							.describe('Type of error (grammar, spelling, word choice, etc.).'),
						explanation: z
							.object({
								source_language: z
									.string()
									.describe(
										'Explanation of the error and correction in the source language.'
									),
								target_language: z
									.string()
									.describe(
										'Explanation of the error and correction in the target language.'
									),
							})
							.describe('Dual-language explanations of the error and correction.'),
					})
				)
				.describe(
					'All errors found in all paragraphs with their corrections and explanations.'
				),
		})
	),
});

export class LLMService {
	private openai: OpenAI | null = null;
	private initPromise: Promise<void> | null = null;

	constructor(private readonly getWordService: () => WordService) {
		this.initPromise = this.initializeOpenAI();
	}

	private async initializeOpenAI(): Promise<void> {
		const llmConfig = await getLLMConfig();
		this.openai = new OpenAI({ apiKey: llmConfig.openAIKey });
	}

	private async ensureInitialized(): Promise<OpenAI> {
		if (this.initPromise) {
			await this.initPromise;
			this.initPromise = null;
		}
		if (!this.openai) {
			throw new Error('OpenAI client not initialized');
		}
		return this.openai;
	}

	private getLanguageName(language: Language): string {
		switch (language) {
			case 'EN':
				return 'English';
			case 'VI':
				return 'Vietnamese';
			default:
				return language;
		}
	}

	async generateRandomTerms(params: GenerateRandomTermsParams): Promise<RandomWord[]> {
		const { keywordTerms, excludesTerms, maxTerms, excludeCollectionIds } = params;

		const allFetchedWords: Word[] = (
			await Promise.all(
				excludeCollectionIds.map(async (id) =>
					this.getWordService().getWordsByCollection(params.userId, id)
				)
			)
		).flat();

		const seenIds = new Set<string | number>();
		const excludeCollectionsWords: Word[] = allFetchedWords.filter((word) => {
			const wordId = word.id;
			if (seenIds.has(wordId)) {
				return false;
			}
			seenIds.add(wordId);
			return true;
		});

		const allExcludes = [
			...excludesTerms,
			...excludeCollectionsWords.map((word: Word) => word.term),
		];

		const openai = await this.ensureInitialized();
		const llmConfig = await getLLMConfig();
		const completion = await openai.chat.completions.parse({
			model: llmConfig.openAIModel,
			messages: [
				{
					role: 'system',
					content: `Generate exactly ${maxTerms} unique vocabulary terms in ${
						params.target_language
					} for ${params.source_language} learners.

Requirements:
- Related to keywords: ${keywordTerms.join(', ')}
- Exclude: ${allExcludes.join(', ')}
- ALWAYS use lowercase unless proper nouns or grammatically required
- Mix of nouns, verbs, adjectives
- Appropriate difficulty and culturally relevant

Ensure no excluded terms and maintain thematic consistency.`,
				},
			],
			temperature: 0.6,
			max_tokens: 6000,
			response_format: zodResponseFormat(
				z.object({
					words: z.array(RandomWordSchema),
				}),
				'event'
			),
		});

		const randomWords = completion.choices[0]?.message.parsed;
		if (!randomWords) {
			throw new Error('Failed to generate terms');
		}

		return randomWords.words as RandomWord[];
	}

	async generateWordDetails(
		terms: string[],
		source_language: Language,
		target_language: Language
	): Promise<Word[]> {
		const existingWords = await this.getWordService().getWordsByTerms(terms);
		const results: Word[] = [];

		const termsToProcess = new Set(terms);

		for (const word of existingWords) {
			results.push(word);
			termsToProcess.delete(word.term);
		}

		if (termsToProcess.size > 0) {
			const openai = await this.ensureInitialized();
			const llmConfig = await getLLMConfig();
			const completion = await openai.chat.completions.parse({
				model: llmConfig.openAIModel,
				messages: [
					{
						role: 'system',
						content: `Generate detailed word information for these ${target_language} terms: ${Array.from(
							termsToProcess
						).join(', ')}

For each term provide:
1. Clear definitions in ${target_language} (primary/secondary meanings)
2. Accurate IPA phonetic transcription with stress markers
3. Explanations in both ${target_language} and ${source_language}
4. 2-3 practical example sentences in both languages

Ensure linguistic accuracy, natural contemporary usage, and cultural appropriateness.`,
					},
				],
				temperature: 0.5,
				max_tokens: 12000,
				response_format: zodResponseFormat(
					z.object({
						words: z.array(RandomWordDetailSchema),
					}),
					'wordDetail'
				),
			});

			const details = completion.choices[0]?.message.parsed;
			if (!details) {
				throw new Error('Failed to generate word details');
			}

			for (const wordDetail of details.words) {
				results.push(
					await this.getWordService().createWordWithRandomWordDetail(wordDetail)
				);
			}
		}

		return results;
	}

	async generateParagraph(params: GenerateParagraphParams): Promise<string[]> {
		const { keywords, language, difficulty, count, sentenceCount } = params;
		const systemPrompt = `Create exactly ${count} distinct paragraphs in ${language} for ${difficulty.toLowerCase()} learners.

Requirements:
- Focus on themes: ${keywords.join(', ')}
- ${
			sentenceCount != null
				? `Approximately ${sentenceCount} sentences per paragraph`
				: 'Appropriate length'
		}
- Engaging, culturally appropriate content
- Perfect grammar and spelling
- Suitable for translation practice
- Vocabulary appropriate for ${difficulty.toLowerCase()} level
- Each paragraph must be unique and offer different challenges

Ensure content is meaningful, relatable, and aids vocabulary retention.`;

		const responseSchema = z.object({
			paragraphs: z.array(z.string()),
		});

		const maxRetries = 3;
		let retryCount = 0;
		let generatedParagraphs: string[] | null = null;

		while (retryCount < maxRetries && !generatedParagraphs) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.parse({
					model: llmConfig.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 1000 * count, // Consider adjusting if sentenceCount implies longer paragraphs
					response_format: zodResponseFormat(responseSchema, 'generateParagraphs'),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.paragraphs ||
					parsedResponse.paragraphs.length !== count
				) {
					throw new Error(
						`LLM did not return the expected number of paragraphs. Expected ${count}, got ${
							parsedResponse?.paragraphs?.length || 0
						}.`
					);
				}
				generatedParagraphs = parsedResponse.paragraphs;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);

				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraphs after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}

				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedParagraphs) {
			throw new Error('Failed to generate paragraphs after retries.');
		}

		return generatedParagraphs;
	}

	async generateExercises(
		params: GenerateExercisesParams
	): Promise<z.infer<typeof ExerciseSchema>[]> {
		const { paragraph, keywords, language, difficulty } = params;

		const systemPrompt = `Create 3-5 diverse exercises based on this ${language} paragraph for ${difficulty.toLowerCase()} learners:
"${paragraph}"

Requirements:
- Include at least one of each: Fill in the Blank, Multiple Choice, Matching
- Focus on keywords: ${keywords.join(', ')}
- Test vocabulary, grammar, and comprehension
- Provide clear questions, correct answers, and explanations
- For multiple choice: include 3-4 plausible options
- Ensure single correct answers and educational value
- Progressive difficulty within the set
- Real-world relevance and clear instructions

Base all exercises directly on the paragraph content.`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedExercises: z.infer<typeof ExerciseSchema>[] | null = null;

		while (retryCount < maxRetries && !generatedExercises) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.parse({
					model: llmConfig.openAIModel,
					messages: [
						{
							role: 'system',
							content: systemPrompt,
						},
					],
					temperature: 0.7,
					max_tokens: 2000,
					response_format: zodResponseFormat(
						ExercisesResponseSchema,
						'generateExercises'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;
				if (!parsedResponse || !parsedResponse.exercises) {
					throw new Error('Empty or invalid response from LLM for exercises.');
				}
				generatedExercises = parsedResponse.exercises;
			} catch (error) {
				retryCount++;
				console.error(`OpenAI API error (attempt ${retryCount}/${maxRetries}):`, error);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate exercises after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedExercises) {
			throw new Error('Failed to generate exercises after retries.');
		}

		return generatedExercises;
	}

	async evaluateTranslation(
		params: EvaluateTranslationParams
	): Promise<TranslationEvaluationResult> {
		const { original_text, translated_text, source_language, target_language } = params;

		const systemPrompt = `Evaluate this translation quality:

**Original (${source_language}):** "${original_text}"
**Translation (${target_language}):** "${translated_text}"

Assess:
1. **Accuracy**: Meaning preservation, completeness, precision
2. **Language Quality**: Grammar, syntax, vocabulary, naturalness
3. **Fluency**: Readability, coherence, flow
4. **Cultural Adaptation**: Appropriate register and cultural sensitivity

**Scoring (1-10):**
- 9-10: Exceptional quality
- 7-8: Good with minor issues
- 5-6: Adequate, needs refinement
- 3-4: Significant issues
- 1-2: Major problems

Provide constructive feedback in both ${source_language} and ${target_language}.

Include detailed evaluation and specific improvement suggestions in both languages. Be constructive, specific, educational, and encouraging.`;

		try {
			const openai = await this.ensureInitialized();
			const llmConfig = await getLLMConfig();
			const completion = await openai.chat.completions.parse({
				model: llmConfig.openAIModel,
				messages: [
					{
						role: 'system',
						content: systemPrompt,
					},
				],
				temperature: 0.3,
				max_tokens: 1000,
				response_format: zodResponseFormat(
					TranslationEvaluationSchema,
					'evaluateTranslation'
				),
			});

			const evaluationResult = completion.choices[0]?.message.parsed;

			if (!evaluationResult) {
				throw new Error('LLM did not return a valid evaluation.');
			}
			return evaluationResult as TranslationEvaluationResult;
		} catch (error) {
			console.error('Error evaluating translation with OpenAI:', error);
			throw new Error(
				`Failed to evaluate translation: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
		}
	}

	async generateQuestions(params: GenerateQuestionsParams): Promise<string[]> {
		const { paragraph, language, questionCount } = params;

		const systemPrompt = `Create exactly ${questionCount} comprehension questions for this ${language} paragraph:
"${paragraph}"

Requirements:
- Mix question types: literal (30-40%), inferential (40-50%), analytical (10-20%)
- Cover main ideas, details, vocabulary, cause/effect, sequence
- Use varied question formats (who, what, why, how)
- Clear, unambiguous wording suitable for ${language} learners
- Answerable from paragraph content only
- Avoid simple yes/no questions
- Balance difficulty levels
- Promote deeper understanding

Format as JSON with "questions" array containing ${questionCount} strings.`;

		const maxRetries = 3;
		let retryCount = 0;
		let generatedQuestions: string[] | null = null;

		while (retryCount < maxRetries && !generatedQuestions) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.parse({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.6,
					max_tokens: 500 + questionCount * 100, // Adjusted token limit
					response_format: zodResponseFormat(
						GeneratedQuestionsSchema,
						'generateQuestions'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.questions ||
					parsedResponse.questions.length !== questionCount
				) {
					throw new Error(
						`LLM did not return the expected number of questions. Expected ${questionCount}, got ${
							parsedResponse?.questions?.length || 0
						}.`
					);
				}
				generatedQuestions = parsedResponse.questions;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateQuestions, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate questions after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!generatedQuestions) {
			throw new Error('Failed to generate questions after retries.');
		}
		return generatedQuestions;
	}

	async generateParagraphWithQuestions(
		params: GenerateParagraphWithQuestionsParams
	): Promise<ParagraphWithQuestionsResult> {
		const { keywords, language, difficulty, sentenceCount, questionCount } = params;

		const sentenceRequirement = sentenceCount
			? `The paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		const systemPrompt = `Create 1 paragraph in ${language} for ${difficulty.toLowerCase()} learners with ${questionCount} comprehension questions.

Paragraph requirements:
- Naturally incorporate keywords: ${keywords.join(', ')}
${sentenceRequirement ? `- ${sentenceRequirement}` : ''}
- Perfect grammar and logical flow
- Vocabulary appropriate for ${difficulty.toLowerCase()} level
- Engaging and culturally appropriate content

Question requirements:
- Mix: literal (30-40%), inferential (40-50%), analytical (10-20%)
- Cover main ideas, details, vocabulary, cause/effect
- Clear wording, answerable from paragraph only
- Varied difficulty levels
- No simple yes/no questions

Format as JSON:
{"paragraph": "text", "questions": ["q1", "q2", ...]}

Ensure paragraph and questions work as integrated learning unit.`;

		const maxRetries = 3;
		let retryCount = 0;
		let result: ParagraphWithQuestionsResult | null = null;

		while (retryCount < maxRetries && !result) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.parse({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: 1500 + questionCount * 100, // Adjusted token limit
					response_format: zodResponseFormat(
						ParagraphWithQuestionsSchema,
						'generateParagraphWithQuestions'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.paragraph ||
					!parsedResponse.questions ||
					parsedResponse.questions.length !== questionCount
				) {
					throw new Error(
						`LLM did not return the expected format. Expected 1 paragraph and ${questionCount} questions, got paragraph: ${
							parsedResponse?.paragraph ? 'yes' : 'no'
						}, questions: ${parsedResponse?.questions?.length || 0}.`
					);
				}

				result = {
					paragraph: parsedResponse.paragraph,
					questions: parsedResponse.questions,
				};
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateParagraphWithQuestions, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate paragraph with questions after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate paragraph with questions after retries.');
		}
		return result;
	}

	async evaluateAnswers(params: EvaluateAnswersParams): Promise<AnswerEvaluationResult[]> {
		const { paragraph, questions, answers, qna_language, feedback_native_language } = params;

		if (questions.length !== answers.length) {
			throw new Error('The number of questions and answers must match.');
		}
		if (questions.length === 0) {
			return [];
		}

		let questionAnswerPairs = '';
		questions.forEach((q, i) => {
			questionAnswerPairs += `Question ${i + 1}: "${q}"\nUser's Answer ${i + 1}: "${
				answers[i]
			}"\n\n`;
		});

		const systemPrompt = `Evaluate these question-answer pairs based on the reference paragraph.

**Reference Paragraph (${qna_language}):**
"${paragraph}"

**Question-Answer Pairs (${qna_language}):**
${questionAnswerPairs}

For each answer, assess:
1. **Content accuracy** relative to paragraph
2. **Completeness** of response
3. **Language quality** in ${qna_language}
4. **Understanding depth**

**Scoring (1-5):**
- 5: Excellent - complete, accurate, well-expressed
- 4: Good - mostly correct, minor issues
- 3: Satisfactory - adequate with some inaccuracies
- 2: Needs improvement - significant issues
- 1: Poor - major inaccuracies

**Correctness:** True if fundamentally accurate, False if significant errors.

**Feedback:** Provide constructive, encouraging feedback in both ${qna_language} and ${feedback_native_language}. Acknowledge strengths, explain errors, offer improvement guidance.

**Suggested answers:** Include when beneficial for learning.

Focus on educational value and student growth.`;

		const maxRetries = 3;
		let retryCount = 0;
		let evaluationResults: AnswerEvaluationResult[] | null = null;

		while (retryCount < maxRetries && !evaluationResults) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.parse({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.3,
					max_tokens: 1500 + questions.length * 300, // Adjusted token limit
					response_format: zodResponseFormat(
						AllAnswersEvaluationSchema,
						'evaluateAnswers'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;

				if (
					!parsedResponse ||
					!parsedResponse.evaluations ||
					parsedResponse.evaluations.length !== questions.length
				) {
					throw new Error(
						`LLM did not return the expected number of evaluations. Expected ${
							questions.length
						}, got ${parsedResponse?.evaluations?.length || 0}.`
					);
				}
				evaluationResults = parsedResponse.evaluations as AnswerEvaluationResult[];
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (evaluateAnswers, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to evaluate answers after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}
		if (!evaluationResults) {
			throw new Error('Failed to evaluate answers after retries.');
		}
		return evaluationResults;
	}

	async generateGrammarPractice(
		params: GrammarPracticeParams
	): Promise<GrammarPracticeResultItem[]> {
		const {
			keywords,
			language,
			source_language: _source_language,
			target_language: _target_language,
			difficulty,
			count,
			sentenceCount,
			errorDensity = 'medium',
		} = params;

		const sentenceRequirement = sentenceCount
			? `Each paragraph should have approximately ${sentenceCount} sentences.`
			: '';

		const systemPrompt = `Create exactly ${count} grammar practice paragraphs in ${language} for ${difficulty.toLowerCase()} learners.

Requirements:
- Include keywords: ${keywords.join(', ')}
${sentenceRequirement ? `- ${sentenceRequirement}` : ''}
- Error density: ${errorDensity} (low: 1-2, medium: 3-4, high: 5-6 errors per paragraph)

Error types for ${difficulty.toLowerCase()} learners:
- Grammar (40-50%): verb tense, subject-verb agreement, articles, prepositions, word order
- Vocabulary (25-35%): word choice, false friends, collocations, register
- Spelling/Mechanics (15-25%): spelling, punctuation, capitalization

For each paragraph provide:
1. Error version with intentional mistakes
2. Corrected version
3. Error documentation with explanations in both ${this.getLanguageName(
			params.source_language
		)} and ${this.getLanguageName(params.target_language)}

Ensure engaging topics, cultural appropriateness, and realistic learner mistakes.`;

		const maxRetries = 3;
		let retryCount = 0;
		let result: GrammarPracticeResultItem[] = [];

		while (retryCount < maxRetries && !result.length) {
			try {
				const openai = await this.ensureInitialized();
				const llmConfig = await getLLMConfig();
				const completion = await openai.chat.completions.parse({
					model: llmConfig.openAIModel,
					messages: [{ role: 'system', content: systemPrompt }],
					temperature: 0.7,
					max_tokens: 2000 * count,
					response_format: zodResponseFormat(
						GrammarPracticeResponseSchema,
						'generateGrammarPractice'
					),
				});

				const parsedResponse = completion.choices[0]?.message.parsed;
				if (!parsedResponse || !parsedResponse.paragraphs) {
					throw new Error(`LLM did not return the expected number of paragraphs`);
				}
				result = parsedResponse.paragraphs;
			} catch (error) {
				retryCount++;
				console.error(
					`OpenAI API error (generateGrammarPractice, attempt ${retryCount}/${maxRetries}):`,
					error
				);
				if (retryCount >= maxRetries) {
					throw new Error(
						`Failed to generate grammar practice after ${maxRetries} attempts: ${
							error instanceof Error ? error.message : String(error)
						}`
					);
				}
				const delay = Math.pow(2, retryCount) * 1000;
				await new Promise((resolve) => setTimeout(resolve, delay));
			}
		}

		if (!result) {
			throw new Error('Failed to generate grammar practice after retries.');
		}
		return result;
	}
}
